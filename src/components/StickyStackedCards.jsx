import React, { useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

const StickyStackedCards = () => {
  const { scrollYProgress } = useScroll();
  useEffect(() => {
    console.log("progress: ",scrollYProgress.get());
  }, [scrollYProgress.get()]);

  // Transform values for each card based on scroll progress
  const card1Scale = useTransform(scrollYProgress, [0, 0.33, 0.6], [1, 0.95, 0.7]);
  const card1Blur = useTransform(scrollYProgress, [0, 0.33, 0.6], [0, 10, 20]);

  const card2Scale = useTransform(scrollYProgress, [0.3, 0.43, 0.8], [1, 0.95, 0.7]);
  const card2Blur = useTransform(scrollYProgress, [0.3, 0.43, 0.9], [0, 10, 20]);

  const cards = [
    {
      id: 1,
      title: "應用層",
      subtitle: "四大AI能力",
      description: "直接面向業務應用的AI產品和服務",
      number: "1",
      bgColor: "from-blue-50/90 to-indigo-100/90",
      borderColor: "border-blue-200/30",
      numberBg: "bg-blue-600",
      titleColor: "text-blue-600",
      gradientFrom: "from-blue-200/20",
      gradientTo: "from-indigo-200/20",
      scale: card1Scale,
      zIndex: 10,
      blur: card1Blur,
      services: [
        {
          name: "Model.AI",
          description: "用戶畫像與行為分析",
          bgColor: "from-blue-400/85 to-blue-500/85",
          borderColor: "border-blue-300/40"
        },
        {
          name: "Strategy.AI", 
          description: "智能決策與運營優化",
          bgColor: "from-emerald-400/85 to-emerald-500/85",
          borderColor: "border-emerald-300/40"
        },
        {
          name: "Chat.AI",
          description: "智能聊天機器人",
          bgColor: "from-purple-400/85 to-purple-500/85", 
          borderColor: "border-purple-300/40"
        },
        {
          name: "AR.Digital",
          description: "虛擬人與數字化身",
          bgColor: "from-amber-400/85 to-orange-500/85",
          borderColor: "border-amber-300/40"
        }
      ]
    },
    {
      id: 2,
      title: "平台層",
      subtitle: "三大核心AI平台", 
      description: "提供核心AI模型訓練、算法和數據能力",
      number: "2",
      bgColor: "from-emerald-50/90 to-teal-100/90",
      borderColor: "border-emerald-200/30",
      numberBg: "bg-green-600",
      titleColor: "text-green-600",
      gradientFrom: "from-emerald-200/20",
      gradientTo: "from-teal-200/20",
      scale: card2Scale,
      blur: card2Blur,
      zIndex: 20,
      services: [
        {
          name: "MXC.AI",
          description: "算法遷移訓練及模型開放",
          bgColor: "from-teal-400/85 to-teal-500/85",
          borderColor: "border-teal-300/40"
        },
        {
          name: "Rochat.AI",
          description: "行業垂直大模型訓練",
          bgColor: "from-indigo-400/85 to-indigo-500/85",
          borderColor: "border-indigo-300/40"
        },
        {
          name: "FL.AI",
          description: "隱私加密計算技術",
          bgColor: "from-sky-400/85 to-sky-500/85",
          borderColor: "border-sky-300/40"
        }
      ]
    },
    {
      id: 3,
      title: "基礎設施層",
      subtitle: "兩大核心引擎",
      description: "整個框架的技術基石",
      number: "3",
      bgColor: "from-purple-50/90 to-violet-100/90",
      borderColor: "border-purple-200/30", 
      numberBg: "bg-purple-600",
      titleColor: "text-purple-600",
      gradientFrom: "from-purple-200/20",
      gradientTo: "from-violet-200/20",
      scale: useTransform(scrollYProgress, [0, 1], [1, 1]), // No scaling for the last card
      blur: useTransform(scrollYProgress, [0, 1], [0, 0]), // No blur for the last card
      opacity: useTransform(scrollYProgress, [0, 1], [1, 1]), // No opacity change for the last card
      zIndex: 30,
      services: [
        {
          name: "模型訓練引擎",
          description: "行業模型預測與算法訓練",
          bgColor: "from-indigo-400/85 to-indigo-500/85",
          borderColor: "border-indigo-300/40"
        },
        {
          name: "數據加密引擎", 
          description: "聯邦學習與分布式計算",
          bgColor: "from-violet-400/85 to-violet-500/85",
          borderColor: "border-violet-300/40"
        }
      ]
    }
  ];

  return (
    <div className="relative space-y-4">
      {cards.map((card) => (
        <motion.div
          key={card.id}
          className={`overflow-hidden sticky top-24 bg-gradient-to-br ${card.bgColor} backdrop-blur-sm rounded-xl shadow-lg border ${card.borderColor}`}
          style={{
            scale: card.scale,
            zIndex: card.zIndex,
            // filter: card.blur ? `blur(${card.blur.get()}px)` : 'none',
            filter: `blur(${card.blur.get()}px)`
          }}
        >
          <span className="text-black">Blur: {card.blur.get()}</span>
          <span className="text-black">Scale: {JSON.stringify(card.scale)}</span>
          <div className={`absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl ${card.gradientFrom} to-transparent rounded-full blur-3xl`}></div>
          <div className={`absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr ${card.gradientTo} to-transparent rounded-full blur-3xl`}></div>
          <div className="relative z-10 w-full grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 min-h-[500px]">
            {/* Left Side - Description */}
            <div className="flex flex-col justify-center">
              <div className="flex items-center mb-8">
                <div className={`w-16 h-16 ${card.numberBg} rounded-lg flex items-center justify-center mr-6`}>
                  <span className="text-white font-bold text-2xl">{card.number}</span>
                </div>
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-3">{card.title}</h3>
                  <h4 className={`text-xl font-semibold ${card.titleColor} mb-4`}>{card.subtitle}</h4>
                  <p className="text-gray-600 text-lg">{card.description}</p>
                </div>
              </div>
              <div className="space-y-4 text-gray-600">
                <p className="text-lg leading-relaxed">
                  {card.id === 1 && "通過四大核心AI產品，為B2C行業用戶提供全方位的智能化解決方案，涵蓋用戶畫像、智能決策、聊天機器人和虛擬人技術。"}
                  {card.id === 2 && "通過三大核心AI平台，提供算法遷移、大模型訓練和隱私計算的完整技術支撐，為上層應用提供強大的AI能力基礎。"}
                  {card.id === 3 && "作為整個AI技術框架的基礎支撐，提供模型訓練和數據加密計算的核心能力，確保整個系統的安全性和可靠性。"}
                </p>
                <ul className="space-y-2 text-base">
                  {card.id === 1 && (
                    <>
                      <li className="flex items-center"><span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>用戶畫像與行為分析</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>智能決策與運營優化</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>聊天機器人與虛擬人技術</li>
                    </>
                  )}
                  {card.id === 2 && (
                    <>
                      <li className="flex items-center"><span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>算法遷移訓練及模型開放</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>行業垂直大模型訓練</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>隱私加密計算技術</li>
                    </>
                  )}
                  {card.id === 3 && (
                    <>
                      <li className="flex items-center"><span className="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>行業模型預測與算法訓練</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>聯邦學習與分布式計算</li>
                      <li className="flex items-center"><span className="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>數據隱私與安全保護</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
            
            {/* Right Side - Services */}
            <div className="space-y-4">
              {card.services.map((service, serviceIndex) => (
                <div key={serviceIndex} className={`relative overflow-hidden bg-gradient-to-r ${service.bgColor} p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border ${service.borderColor}`}>
                  <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
                  <div className="relative z-10 flex items-center space-x-4">
                    <div className="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold mb-2">{service.name}</h4>
                      <p className="text-white/90 text-sm leading-relaxed">{service.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default StickyStackedCards;
