---
import MainLayout from '../layouts/MainLayout.astro';
---

<MainLayout title="產品與技術 - 青釭金融科技">
	<!-- Hero Section -->
	<section class="bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 text-white py-24">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="text-4xl md:text-6xl font-bold mb-8">
					技術產品框架
				</h1>
				<p class="text-xl md:text-2xl mb-6 text-gray-200">
					AI+大數據+分布式加密計算
				</p>
				<p class="text-lg text-gray-300 max-w-4xl mx-auto leading-relaxed">
					以覆蓋全國C端用戶數據網絡為基礎，為B2C行業用戶搭建客戶精準運營和自動化服務體系，整合AI+智能決策、AI+智能生成等前沿技術，推動行業用戶實現營銷運營ROI提升和業務數字化轉型
				</p>
			</div>
		</div>
	</section>

	<!-- Framework Overview -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					框架總覽
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					三層架構的AI技術產品體系，從底層基礎設施到上層應用的完整解決方案
				</p>
			</div>
			
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-10">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
					<!-- Core Technologies -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">核心技術驅動</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+大數據</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">分布式加密計算</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">覆蓋全國C端用戶數據網絡</span>
							</div>
						</div>
					</div>
					
					<!-- Target Users -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">目標用戶</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">B2C行業用戶</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">客戶精準運營</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">自動化服務體系</span>
							</div>
						</div>
					</div>
					
					<!-- Core Services -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">核心服務</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+智能決策</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+智能生成</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">營銷運營ROI提升</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Three Layer Architecture -->
	<section class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					三層架構體系
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					從基礎設施到應用層的完整技術堆疊
				</p>
			</div>

			<!-- Sticky Container -->
			<div class="relative space-y-4">
				<!-- Application Layer -->
				<div class="overflow-hidden sticky top-24 z-10 bg-gradient-to-br from-blue-50/90 to-indigo-100/90 backdrop-blur-sm rounded-xl shadow-lg border border-blue-200/30">
					<div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-indigo-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="relative z-10 w-full grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 min-h-[500px]">
						<!-- Left Side - Description -->
						<div class="flex flex-col justify-center">
							<div class="flex items-center mb-8">
								<div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mr-6">
									<span class="text-white font-bold text-2xl">1</span>
								</div>
								<div>
									<h3 class="text-3xl font-bold text-gray-900 mb-3">應用層</h3>
									<h4 class="text-xl font-semibold text-blue-600 mb-4">四大AI能力</h4>
									<p class="text-gray-600 text-lg">直接面向業務應用的AI產品和服務</p>
								</div>
							</div>
							<div class="space-y-4 text-gray-600">
								<p class="text-lg leading-relaxed">通過四大核心AI產品，為B2C行業用戶提供全方位的智能化解決方案，涵蓋用戶畫像、智能決策、聊天機器人和虛擬人技術。</p>
								<ul class="space-y-2 text-base">
									<li class="flex items-center"><span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>用戶畫像與行為分析</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>智能決策與運營優化</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>對話機器人與自動化</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>AR虛擬人技術</li>
								</ul>
							</div>
						</div>
						
						<!-- Right Side - Services -->
						<div class="space-y-4">
							<!-- Model.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-blue-400/85 to-blue-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-blue-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">Model.AI</h4>
										<p class="text-blue-50/95 text-sm">用戶畫像模型 - 客群特征分析、風險評估、客戶行為偏好預測</p>
									</div>
								</div>
							</div>

							<!-- Strategy.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-emerald-400/85 to-emerald-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-emerald-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">Strategy.AI</h4>
										<p class="text-emerald-50/95 text-sm">智能決策工具 - 推薦策略、運營分析、運營RPA工具</p>
									</div>
								</div>
							</div>

							<!-- Robot.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-violet-400/85 to-violet-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-violet-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">Robot.AI</h4>
										<p class="text-violet-50/95 text-sm">聊天機器人 - 文案機器人、招聘機器人、行業場景大模型</p>
									</div>
								</div>
							</div>

							<!-- AR.Digital -->
							<div class="relative overflow-hidden bg-gradient-to-r from-amber-400/85 to-orange-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-amber-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">AR.Digital</h4>
										<p class="text-amber-50/95 text-sm">智能虛擬人 - 數字人客服、交互機器人、AR智能虛擬人技術</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Platform Layer -->
				<div class="overflow-hidden sticky top-24 z-20 bg-gradient-to-br from-emerald-50/90 to-teal-100/90 backdrop-blur-sm rounded-xl shadow-lg border border-emerald-200/30">
					<div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-emerald-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-teal-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="relative z-10 w-full grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 min-h-[500px]">
						<!-- Left Side - Description -->
						<div class="flex flex-col justify-center">
							<div class="flex items-center mb-8">
								<div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mr-6">
									<span class="text-white font-bold text-2xl">2</span>
								</div>
								<div>
									<h3 class="text-3xl font-bold text-gray-900 mb-3">平台層</h3>
									<h4 class="text-xl font-semibold text-green-600 mb-4">三大核心AI平台</h4>
									<p class="text-gray-600 text-lg">提供核心AI模型訓練、算法和數據能力</p>
								</div>
							</div>
							<div class="space-y-4 text-gray-600">
								<p class="text-lg leading-relaxed">通過三大核心AI平台，提供算法遷移、大模型訓練和隱私計算的完整技術支撐，為上層應用提供強大的AI能力基礎。</p>
								<ul class="space-y-2 text-base">
									<li class="flex items-center"><span class="w-2 h-2 bg-green-600 rounded-full mr-3"></span>算法遷移訓練及模型開放</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-green-600 rounded-full mr-3"></span>行業垂直大模型訓練</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-green-600 rounded-full mr-3"></span>隱私加密計算技術</li>
								</ul>
							</div>
						</div>
						
						<!-- Right Side - Services -->
						<div class="space-y-4">
							<!-- MXC.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-teal-400/85 to-teal-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-teal-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">MXC.AI</h4>
										<p class="text-teal-50/95 text-sm">算法遷移訓練及模型能力開放平台 - API管理、畫像標籤、算法模型庫</p>
									</div>
								</div>
							</div>

							<!-- Rochat.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-indigo-400/85 to-indigo-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-indigo-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">Rochat.AI</h4>
										<p class="text-indigo-50/95 text-sm">AI行業垂直類大模型訓練引擎 - 場景知識庫、NLP、GPT技術</p>
									</div>
								</div>
							</div>

							<!-- FL.AI -->
							<div class="relative overflow-hidden bg-gradient-to-r from-sky-400/85 to-sky-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-sky-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">FL.AI</h4>
										<p class="text-sky-50/95 text-sm">基於隱私加密計算技術的AI算法訓練平台 - 隱私請求、匿蹤查詢、聯邦學習</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Infrastructure Layer -->
				<div class="overflow-hidden sticky top-24 z-30 bg-gradient-to-br from-purple-50/90 to-violet-100/90 backdrop-blur-sm rounded-xl shadow-lg border border-purple-200/30">
					<div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-purple-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-violet-200/20 to-transparent rounded-full blur-3xl"></div>
					<div class="relative z-10 w-full grid grid-cols-1 lg:grid-cols-2 gap-8 p-8 min-h-[500px]">
						<!-- Left Side - Description -->
						<div class="flex flex-col justify-center">
							<div class="flex items-center mb-8">
								<div class="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mr-6">
									<span class="text-white font-bold text-2xl">3</span>
								</div>
								<div>
									<h3 class="text-3xl font-bold text-gray-900 mb-3">基礎設施層</h3>
									<h4 class="text-xl font-semibold text-purple-600 mb-4">兩大核心引擎</h4>
									<p class="text-gray-600 text-lg">整個框架的技術基石</p>
								</div>
							</div>
							<div class="space-y-4 text-gray-600">
								<p class="text-lg leading-relaxed">作為整個AI技術框架的基礎支撐，提供模型訓練和數據加密計算的核心能力，確保整個系統的安全性和可靠性。</p>
								<ul class="space-y-2 text-base">
									<li class="flex items-center"><span class="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>行業模型預測與算法訓練</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>聯邦學習與分布式計算</li>
									<li class="flex items-center"><span class="w-2 h-2 bg-purple-600 rounded-full mr-3"></span>數據隱私與安全保護</li>
								</ul>
							</div>
						</div>
						
						<!-- Right Side - Services -->
						<div class="space-y-4">
							<!-- Model Training -->
							<div class="relative overflow-hidden bg-gradient-to-r from-indigo-400/85 to-indigo-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-indigo-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">模型訓練和算法遷移</h4>
										<p class="text-indigo-50/95 text-sm">構建各類行業模型預測和算法訓練引擎 - 支撐MXC.AI和FL.AI平台</p>
									</div>
								</div>
							</div>

							<!-- Data Encryption -->
							<div class="relative overflow-hidden bg-gradient-to-r from-slate-400/85 to-slate-500/85 p-6 rounded-xl shadow-lg text-white hover:shadow-xl transition-all duration-300 border border-slate-300/40">
								<div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
								<div class="relative z-10 flex items-center space-x-4">
									<div class="w-12 h-12 bg-white/25 rounded-xl flex items-center justify-center flex-shrink-0 backdrop-blur-sm border border-white/20">
										<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
										</svg>
									</div>
									<div>
										<h4 class="text-lg font-semibold mb-1">數據加密計算和流通</h4>
										<p class="text-slate-50/95 text-sm">基於FL分布式加密算法網絡生態 - 聯邦學習、分布式加密算法</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Spacer for scroll completion -->
				<div class="h-32"></div>
			</div>
		</div>
	</section>

	<!-- Technology Flow -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					技術流程與關係
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					從底層到上層的數據流和技術流
				</p>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
					<!-- Data Flow -->
					<div class="text-center">
						<div class="w-20 h-20 bg-blue-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">數據流向</h3>
						<p class="text-gray-600">數據和算法能力由底層向上層流動和支撐</p>
					</div>

					<!-- Technology Integration -->
					<div class="text-center">
						<div class="w-20 h-20 bg-green-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">技術整合</h3>
						<p class="text-gray-600">三大平台分別負責算法模型、大模型訓練和隱私計算</p>
					</div>

					<!-- Business Application -->
					<div class="text-center">
						<div class="w-20 h-20 bg-purple-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">商業應用</h3>
						<p class="text-gray-600">通過四大AI產品向B2C行業用戶提供具體服務</p>
					</div>
				</div>

				<div class="mt-16 text-center">
					<div class="inline-flex items-center space-x-6">
						<div class="flex items-center">
							<div class="w-4 h-4 bg-purple-600 rounded-full mr-2"></div>
							<span class="text-gray-700">基礎設施層</span>
						</div>
						<div class="w-12 h-px bg-gray-300"></div>
						<div class="flex items-center">
							<div class="w-4 h-4 bg-green-600 rounded-full mr-2"></div>
							<span class="text-gray-700">平台層</span>
						</div>
						<div class="w-12 h-px bg-gray-300"></div>
						<div class="flex items-center">
							<div class="w-4 h-4 bg-blue-600 rounded-full mr-2"></div>
							<span class="text-gray-700">應用層</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Core Advantages -->
	<section class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					核心技術優勢
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					融合數據+算力+算法+工具的完整解決方案
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">數據</h3>
					<p class="text-gray-600">覆蓋全國C端用戶數據網絡</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">算力</h3>
					<p class="text-gray-600">分布式加密計算能力</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">算法</h3>
					<p class="text-gray-600">AI+智能決策與生成算法</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-orange-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">工具</h3>
					<p class="text-gray-600">完整的AI應用工具鏈</p>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-20 bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h2 class="text-3xl md:text-4xl font-bold mb-6">
				體驗AI+智能決策技術
			</h2>
			<p class="text-xl mb-10 text-gray-300 max-w-2xl mx-auto">
				了解我們如何幫助您實現營銷運營ROI提升和業務數字化轉型
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/#" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors">
					申請產品演示
				</a>
				<a href="/market" class="bg-transparent border-2 border-gray-300 text-gray-300 hover:bg-gray-300 hover:text-gray-900 px-8 py-4 rounded-lg font-semibold transition-colors">
					查看成功案例
				</a>
			</div>
		</div>
	</section>
</MainLayout> 