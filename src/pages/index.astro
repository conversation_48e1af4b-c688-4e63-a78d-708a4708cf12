---
import MainLayout from '../layouts/MainLayout.astro';
import StickyStackedCards from '../components/StickyStackedCards.jsx';
---

<MainLayout title="青釭金融科技 - 構建基於AI+智能決策的價值流量運營平台">
	<!-- Hero Section -->
	<section class="relative min-h-screen flex items-center justify-center bg-white overflow-hidden">
		<!-- Background Elements -->
		<div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-white"></div>
		<div class="absolute top-20 right-20 w-96 h-96 bg-blue-100/20 rounded-full blur-3xl"></div>
		<div class="absolute bottom-20 left-20 w-80 h-80 bg-indigo-100/20 rounded-full blur-3xl"></div>
		
		<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
			<div class="text-center">
				<!-- Company Name -->
				<div class="mb-8">
					<h1 class="text-5xl md:text-7xl font-light text-slate-900 tracking-tight mb-4">
						青釭<span class="font-normal text-blue-600">金融科技</span>
					</h1>
					<div class="w-24 h-1 bg-blue-600 mx-auto"></div>
				</div>
				
				<!-- Main Tagline -->
				<p class="text-2xl md:text-3xl font-light text-slate-700 mb-8 max-w-4xl mx-auto leading-relaxed">
					構建基於"AI+智能決策"的<br class="hidden md:block"/>
					<span class="text-blue-600 font-normal">價值流量運營平台</span>
				</p>
				
				<!-- Description -->
				<p class="text-lg text-slate-600 max-w-4xl mx-auto mb-12 leading-relaxed font-light">
					以「AI+大數據」技術為驅動，依託「隱私計算+加密傳輸」的演算法訓練和網路體系，<br class="hidden lg:block"/>
					為包括金融、零售、教育、互聯網平台等在內的B2C行業輸出AI+智能決策和AI+智能生成技術及業務服務
				</p>
				
				<!-- CTA Buttons -->
				<div class="flex flex-col sm:flex-row gap-6 justify-center">
					<a href="/products" class="group inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-600/25">
						探索產品
						<svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
						</svg>
					</a>
					<a href="/#" class="inline-flex items-center justify-center px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-xl font-medium hover:border-blue-600 hover:text-blue-600 transition-all duration-300">
						聯繫我們
					</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Company Mission -->
	<section class="py-24 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-20">
				<h2 class="text-4xl md:text-5xl font-light text-slate-900 mb-6">
					企業<span class="text-blue-600">使命</span>
				</h2>
				<div class="w-16 h-1 bg-blue-600 mx-auto mb-8"></div>
				<p class="text-xl text-slate-600 max-w-4xl mx-auto font-light leading-relaxed">
					青釭金融科技有限公司致力於構建基於"AI+智能決策"的價值流量運營平台，<br class="hidden lg:block"/>
					推動行業用戶實現營銷運營ROI提升和業務數位化轉型
				</p>
			</div>
			
			<div class="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
				<div class="group text-center p-8 rounded-2xl border border-slate-200 hover:border-blue-200 hover:shadow-xl transition-all duration-500 bg-white">
					<div class="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 transition-colors duration-300">
						<svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
						</svg>
					</div>
					<h3 class="text-xl font-medium mb-4 text-slate-900">隱私計算技術</h3>
					<p class="text-slate-600 font-light leading-relaxed">
						基於隱私計算+加密傳輸的演算法訓練和網路體系，確保數據安全與隱私保護
					</p>
				</div>
				
				<div class="group text-center p-8 rounded-2xl border border-slate-200 hover:border-blue-200 hover:shadow-xl transition-all duration-500 bg-white">
					<div class="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 transition-colors duration-300">
						<svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"/>
						</svg>
					</div>
					<h3 class="text-xl font-medium mb-4 text-slate-900">AI+大數據</h3>
					<p class="text-slate-600 font-light leading-relaxed">
						搭建C端用戶動態數據畫像生態，構建智能化數據治理體系
					</p>
				</div>
				
				<div class="group text-center p-8 rounded-2xl border border-slate-200 hover:border-blue-200 hover:shadow-xl transition-all duration-500 bg-white">
					<div class="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 transition-colors duration-300">
						<svg class="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
						</svg>
					</div>
					<h3 class="text-xl font-medium mb-4 text-slate-900">多元觸達通道</h3>
					<p class="text-slate-600 font-light leading-relaxed">
						融合「富媒體通訊+互聯網平台」多種類用戶觸達通道搭建價值流量轉化及運營體系
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- AI Architecture Framework -->
	<section class="py-32 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
		<!-- Background Elements -->
		<!-- <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-200/10 to-transparent rounded-full blur-3xl"></div>
		<div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-indigo-200/10 to-transparent rounded-full blur-3xl"></div> -->

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-20">
				<h2 class="text-4xl md:text-6xl font-light text-slate-900 mb-6">
					三層<span class="text-blue-600 font-normal">AI架構</span>
				</h2>
				<div class="w-20 h-1 bg-blue-600 mx-auto mb-8"></div>
				<p class="text-xl md:text-2xl text-slate-600 max-w-5xl mx-auto font-light leading-relaxed">
					從應用到基礎設施的完整AI技術棧，<br class="hidden lg:block"/>
					<span class="text-blue-600 font-medium">構建智能化商業生態系統</span>
				</p>
				<div class="mt-8 text-lg text-slate-500 font-light">
					滾動探索我們的技術架構層次
				</div>
			</div>

			<!-- Sticky Stacked Cards Component -->
			<StickyStackedCards client:load />
		</div>
	</section>

	<!-- Team Introduction -->
	<section class="py-24 bg-slate-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-20">
				<h2 class="text-4xl md:text-5xl font-light text-slate-900 mb-6">
					核心<span class="text-blue-600">團隊</span>
				</h2>
				<div class="w-16 h-1 bg-blue-600 mx-auto mb-8"></div>
				<p class="text-xl text-slate-600 max-w-4xl mx-auto font-light leading-relaxed">
					具有豐富行業經驗的專業團隊，致力於推動人工智慧技術的創新與應用
				</p>
			</div>

			<!-- Team Summary - Updated Layout -->
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
				<!-- CEO Henry - Left Block (1/3 width) -->
				<div class="bg-white rounded-3xl p-10 shadow-lg border border-slate-200">
					<div class="text-center">
						<div class="w-24 h-24 mx-auto mb-6 rounded-full overflow-hidden border-3 border-slate-200">
							<img
								src="/avatar/henry_ng.webp"
								alt="Henry Ng"
								class="w-full h-full object-cover"
							/>
						</div>
						<h3 class="text-2xl font-medium text-slate-900 mb-2">Henry Ng</h3>
						<p class="text-blue-600 font-medium mb-4">執行長 (CEO)</p>
						<p class="text-slate-600 font-light text-sm leading-relaxed">
							道元集團有限公司董事長，澳門國際投資協會會長，珠海市政協委員。具20年以上項目投資經驗，專注人工智慧與各行業的深度融合。
						</p>
					</div>
				</div>

				<!-- Team Stats - Right Block (2/3 width) -->
				<div class="lg:col-span-2 bg-white rounded-3xl p-10 shadow-lg border border-slate-200">
					<div class="text-center mb-8">
						<h3 class="text-2xl font-medium text-slate-900 mb-4">團隊實力</h3>
						<p class="text-slate-600 font-light">國內外知名高等院校和科研院所的專業人才匯聚</p>
					</div>
					<div class="grid grid-cols-2 md:grid-cols-4 gap-8">
						<div class="text-center">
							<div class="text-3xl font-light text-blue-600 mb-2">20+</div>
							<p class="text-slate-600 font-light">核心團隊成員</p>
						</div>
						<div class="text-center">
							<div class="text-3xl font-light text-blue-600 mb-2">50+</div>
							<p class="text-slate-600 font-light">團隊總人數</p>
						</div>
						<div class="text-center">
							<div class="text-3xl font-light text-blue-600 mb-2">30%+</div>
							<p class="text-slate-600 font-light">碩博士比例</p>
						</div>
						<div class="text-center">
							<div class="text-3xl font-light text-blue-600 mb-2">10+</div>
							<p class="text-slate-600 font-light">年行業經驗</p>
						</div>
					</div>
					<div class="mt-8 text-center">
						<a href="/about#team" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium group">
							了解更多團隊詳情
							<svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
							</svg>
						</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Core Technology -->
	<section class="py-24 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
				<!-- Left Content -->
				<div>
					<h2 class="text-4xl md:text-5xl font-light text-slate-900 mb-6">
						核心<span class="text-blue-600">技術</span>優勢
					</h2>
					<div class="w-16 h-1 bg-blue-600 mb-8"></div>
					<p class="text-xl text-slate-600 mb-12 font-light leading-relaxed">
						打造行業垂類AI+應用生態體系，提供全方位的智能解決方案
					</p>
					
					<div class="space-y-6">
						<div class="flex items-start group">
							<div class="w-3 h-3 bg-blue-600 rounded-full mt-3 mr-6 flex-shrink-0 group-hover:scale-150 transition-transform duration-300"></div>
							<span class="text-slate-700 text-lg font-light">隱私計算+加密傳輸的演算法訓練</span>
						</div>
						<div class="flex items-start group">
							<div class="w-3 h-3 bg-blue-600 rounded-full mt-3 mr-6 flex-shrink-0 group-hover:scale-150 transition-transform duration-300"></div>
							<span class="text-slate-700 text-lg font-light">C端用戶動態數據畫像生態</span>
						</div>
						<div class="flex items-start group">
							<div class="w-3 h-3 bg-blue-600 rounded-full mt-3 mr-6 flex-shrink-0 group-hover:scale-150 transition-transform duration-300"></div>
							<span class="text-slate-700 text-lg font-light">富媒體通訊+互聯網平台多元觸達</span>
						</div>
						<div class="flex items-start group">
							<div class="w-3 h-3 bg-blue-600 rounded-full mt-3 mr-6 flex-shrink-0 group-hover:scale-150 transition-transform duration-300"></div>
							<span class="text-slate-700 text-lg font-light">價值流量轉化及運營體系</span>
						</div>
					</div>
				</div>

				<!-- Right Content - Technology Showcase -->
				<div class="relative">
					<div class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl p-12 border border-slate-200">
						<div class="text-center">
							<div class="w-20 h-20 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-8">
								<span class="text-white font-light text-3xl">AI</span>
							</div>
							<h4 class="text-2xl font-medium mb-6 text-slate-900">智能決策引擎</h4>
							<p class="text-slate-600 mb-10 font-light leading-relaxed">
								基於AI+大數據技術驅動，為B2C行業用戶提供精準的智能決策支持和業務服務
							</p>
							<div class="grid grid-cols-2 gap-8">
								<div class="text-center">
									<div class="text-3xl font-light text-blue-600 mb-2">99.9%</div>
									<p class="text-slate-600 font-light">準確率</p>
								</div>
								<div class="text-center">
									<div class="text-3xl font-light text-blue-600 mb-2">10ms</div>
									<p class="text-slate-600 font-light">響應時間</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-24 bg-slate-900 relative overflow-hidden">
		<!-- Background Elements -->
		<div class="absolute top-0 right-0 w-96 h-96 bg-blue-600/10 rounded-full blur-3xl"></div>
		<div class="absolute bottom-0 left-0 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"></div>
		
		<div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h2 class="text-4xl md:text-5xl font-light text-white mb-6">
				開啟AI+<span class="text-blue-400">智能決策</span>之旅
			</h2>
			<div class="w-24 h-1 bg-blue-400 mx-auto mb-8"></div>
			<p class="text-xl mb-12 text-slate-300 max-w-3xl mx-auto font-light leading-relaxed">
				讓我們為您的企業提供專業的AI+智能決策解決方案，<br class="hidden md:block"/>
				實現營銷運營ROI提升和業務數位化轉型
			</p>
			<div class="flex flex-col sm:flex-row gap-6 justify-center">
				<a href="/products" class="group inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-600/25">
					查看產品
					<svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
					</svg>
				</a>
				<a href="/#" class="inline-flex items-center justify-center px-8 py-4 border-2 border-slate-400 text-slate-300 rounded-xl font-medium hover:border-blue-400 hover:text-blue-400 transition-all duration-300">
					咨詢服務
				</a>
			</div>
		</div>
	</section>
</MainLayout>
